# Deutsch (Deutschland) translations for PROJECT.
# Copyright (C) 2025 ORGANIZATION
# This file is distributed under the same license as the PROJECT project.
# <PERSON> <<EMAIL>>, 2025.
#
msgid ""
msgstr ""
"Project-Id-Version: PROJECT VERSION\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-07-13 12:58+0900\n"
"PO-Revision-Date: 2025-07-02 12:35+0900\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language: de_DE\n"
"Language-Team: de_DE <<EMAIL>>\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#: __init__.py:52
#, python-brace-format
msgid "无法解析Anki版本: {}"
msgstr "Anki-Version konnte nicht analysiert werden: {}"

#: __init__.py:107
msgid "番茄钟 & 呼吸设置..."
msgstr "Pomodoro & Atmung Einstellungen..."

#: __init__.py:110
msgid "启动呼吸训练"
msgstr "Atemübung starten"

#: __init__.py:119
msgid "警告: 无法添加番茄钟菜单项"
msgstr "Warnung: Menüeintrag für Pomodoro konnte nicht hinzugefügt werden"

#: hooks.py:60 pomodoro/pomodoro_manager.py:103
msgid "番茄钟计时器已停止。"
msgstr "Pomodoro-Timer gestoppt."

#: hooks.py:78
#, python-brace-format
msgid "恭喜完成{target}个番茄钟！建议休息{minutes}分钟。"
msgstr ""
"Glückwunsch zum Abschluss von {target} Pomodoros! Empfohlene Pause: "
"{minutes} Minuten."

#: hooks.py:85
msgid "番茄钟时间到！"
msgstr "Pomodoro beendet! Zeit für eine Pause."

#: hooks.py:124
msgid "呼吸训练已跳过 (无启用阶段)。"
msgstr "Atemübung übersprungen (keine Phasen aktiviert)."

#: hooks.py:129
msgid "呼吸训练已跳过 (循环次数为 0)。"
msgstr "Atemübung übersprungen (Anzahl der Zyklen ist 0)."

#: hooks.py:135
msgid "呼吸训练完成！"
msgstr "Atemübung abgeschlossen!"

#: hooks.py:137
msgid "呼吸训练已跳过。"
msgstr "Atemübung übersprungen."

#: hooks.py:139
msgid "跳过呼吸训练 (主窗口不可见)。"
msgstr "Atemübung übersprungen (Hauptfenster nicht sichtbar)."

#: config/constants.py:48
msgid "⏳休息中："
msgstr "⏳ In Pause:"

#: config/constants.py:49
msgid "⚠️休息上限："
msgstr "⚠️ Pausenlimit erreicht:"

#: config/enums.py:21
msgid "左上角"
msgstr "Oben links"

#: config/enums.py:22
msgid "右上角"
msgstr "Oben rechts"

#: config/enums.py:23
msgid "左下角"
msgstr "Unten links"

#: config/enums.py:24
msgid "右下角"
msgstr "Unten rechts"

#: config/enums.py:25
msgid "上次使用的位置"
msgstr "Zuletzt verwendete Position"

#: config/enums.py:58
msgid "吸气"
msgstr "Einatmen"

#: config/enums.py:64
msgid "屏气 (吸气后)"
msgstr "Atem anhalten (nach Einatmen)"

#: config/enums.py:70
msgid "呼气"
msgstr "Ausatmen"

#: config/enums.py:76
msgid "屏气 (呼气后)"
msgstr "Atem anhalten (nach Ausatmen)"

#: config/enums.py:97
msgid "不显示"
msgstr "Nicht anzeigen"

#: config/enums.py:98
msgid "仅显示图标"
msgstr "Nur Symbol anzeigen"

#: config/enums.py:99
msgid "仅显示倒计时"
msgstr "Nur Timer anzeigen"

#: config/enums.py:100
msgid "仅显示进度"
msgstr "Nur Fortschritt anzeigen"

#: config/enums.py:103
msgid "显示图标+倒计时+进度"
msgstr "Symbol + Timer + Fortschritt anzeigen"

#: config/enums.py:107
msgid "显示图标+倒计时+进度+累计使用时间"
msgstr "Symbol + Timer + Fortschritt + Gesamtnutzung anzeigen"

#: pomodoro/pomodoro_manager.py:55
msgid "本次番茄钟结束"
msgstr "Aktueller Pomodoro beendet"

#: pomodoro/pomodoro_manager.py:83
msgid "番茄钟计时器已被禁用。"
msgstr "Der Pomodoro-Timer wurde deaktiviert."

#: pomodoro/pomodoro_manager.py:96
#, python-brace-format
msgid "番茄钟计时器已启动，时长: {} 分钟。"
msgstr "Pomodoro-Timer gestartet, Dauer: {} Minuten."

#: pomodoro/pomodoro_manager.py:123
msgid "检测到长时间空闲，连胜中断。"
msgstr "Längere Inaktivität erkannt, Serie unterbrochen."

#: pomodoro/pomodoro_manager.py:157
msgid "休息时间过长，番茄钟连胜已清空。"
msgstr "Zu lange pausiert, Pomodoro-Serie zurückgesetzt."

#: ui/version_dialog.py:13
msgid "请更新Anki"
msgstr "Bitte Anki aktualisieren"

#: ui/version_dialog.py:21
#, python-brace-format
msgid "Anki番茄钟 & 呼吸训练插件需要Anki版本 <b>{required_version}</b> 或更高版本才能运行。"
msgstr ""
"Das Anki Pomodoro Timer & Breathe Exercise Add-on benötigt Anki-Version "
"<b>{required_version}</b> oder höher, um zu funktionieren."

#: ui/version_dialog.py:24
#, python-brace-format
msgid "您当前使用的Anki版本是 <b>{current_version}</b>。"
msgstr "Sie verwenden derzeit die Anki-Version <b>{current_version}</b>."

#: ui/version_dialog.py:28
msgid "请更新Anki到最新版本以使用此插件。"
msgstr ""
"Bitte aktualisieren Sie Anki auf die neueste Version, um dieses Add-on zu"
" verwenden."

#: ui/version_dialog.py:34
msgid "跳转到Anki更新页面"
msgstr "Gehe zur Anki-Update-Seite"

#: ui/version_dialog.py:38
msgid "确定"
msgstr "OK"

#: ui/breathing/dialog.py:30 ui/config/dialog.py:36
msgid "呼吸训练"
msgstr "Atemübung"

#: ui/breathing/dialog.py:46
msgid "准备..."
msgstr "Bereit machen..."

#: ui/breathing/dialog.py:53 ui/breathing/dialog.py:84
#, python-brace-format
msgid "循环: {current} / {total}"
msgstr "Zyklus: {current} / {total}"

#: ui/breathing/dialog.py:61
msgid "跳过训练"
msgstr "Training überspringen"

#: ui/circularTimer/core/window.py:48
msgid "番茄钟计时器"
msgstr "Pomodoro-Timer"

#: ui/config/breathing.py:46
msgid "呼吸训练设置"
msgstr "Einstellungen für Atemübungen"

#: ui/config/breathing.py:50
msgid "目标循环次数:"
msgstr "Anzahl der Zyklen:"

#: ui/config/breathing.py:59
msgid "呼吸阶段设置"
msgstr "Einstellungen der Atemphasen:"

#: ui/config/breathing.py:85
msgid "选择音频"
msgstr "Audio auswählen"

#: ui/config/breathing.py:87 ui/config/breathing.py:158
msgid "未选择"
msgstr "Nicht ausgewählt"

#: ui/config/breathing.py:97
msgid "持续时间:"
msgstr "Dauer:"

#: ui/config/breathing.py:105
msgid "秒"
msgstr "Sekunden"

#: ui/config/breathing.py:116
msgid "预计时间: --:--"
msgstr "Geschätzte Zeit: --:--"

#: ui/config/breathing.py:148
#, python-brace-format
msgid "为 {phase_key} 阶段选择音频文件"
msgstr "Audiodatei für die Phase '{phase_key}' auswählen"

#: ui/config/breathing.py:150
msgid "音频文件 (*.wav *.mp3 *.opus *.ogg);;所有文件 (*)"
msgstr "Audiodateien (*.wav *.mp3 *.opus *.ogg);;Alle Dateien (*)"

#: ui/config/dialog.py:28
msgid "番茄钟/呼吸训练设置"
msgstr "Pomodoro & Atmung Einstellungen"

#: ui/config/dialog.py:35 ui/config/general.py:42
msgid "常规设置"
msgstr "Allgemeine Einstellungen"

#: ui/config/dialog.py:98
msgid "预计时间: --:-- (未启用或周期为0)"
msgstr "Geschätzte Zeit: --:-- (nicht aktiviert oder Zyklen = 0)"

#: ui/config/dialog.py:105
#, python-brace-format
msgid "预计时间: {mins:02d}:{secs:02d}"
msgstr "Geschätzte Zeit: {mins:02d}:{secs:02d}"

#: ui/config/dialog.py:111
msgid "预计时间: 计算错误"
msgstr "Geschätzte Zeit: Berechnungsfehler"

#: ui/config/dialog.py:134
msgid "配置已保存"
msgstr "Konfiguration gespeichert"

#: ui/config/dialog.py:142
#, python-brace-format
msgid "保存配置时出错: {}"
msgstr "Fehler beim Speichern der Konfiguration: {}"

#: ui/config/general.py:51
msgid "启用番茄钟插件"
msgstr "Pomodoro-Plugin aktivieren"

#: ui/config/general.py:57
msgid "语言:"
msgstr "Sprache:"

#: ui/config/general.py:66
msgid "全局计时器"
msgstr "Globaler Timer"

#: ui/config/general.py:72
msgid "显示圆形计时器"
msgstr "Kreisförmigen Timer anzeigen"

#: ui/config/general.py:78
msgid "圆形计时器样式:"
msgstr "Stil des kreisförmigen Timers:"

#: ui/config/general.py:91
msgid "计时器窗口位置:"
msgstr "Position des Timer-Fensters:"

#: ui/config/general.py:104
msgid "连胜上限:"
msgstr "Serien-Limit:"

#: ui/config/general.py:109 ui/config/general.py:130
msgid "个番茄钟"
msgstr "Pomodoros"

#: ui/config/general.py:117
msgid "连续完成指定数量的番茄钟后，将进行长休息"
msgstr ""
"Nach Abschluss der festgelegten Anzahl von Pomodoros beginnt eine lange "
"Pause."

#: ui/config/general.py:123
msgid "进度显示阈值:"
msgstr "Schwellenwert für Fortschrittsanzeige:"

#: ui/config/general.py:143
msgid "当目标番茄钟数量超过此值时，将以 '🍅 x N' 的形式显示进度"
msgstr "Wenn die Zielanzahl der Pomodoros diesen Wert überschreitet, wird der Fortschritt als '🍅 x N' angezeigt"

#: ui/config/general.py:152
msgid "番茄钟时长:"
msgstr "Pomodoro-Dauer:"

#: ui/config/general.py:157 ui/config/general.py:171 ui/config/general.py:185
msgid "分钟"
msgstr "Minuten"

#: ui/config/general.py:166
msgid "长休息时长:"
msgstr "Dauer der langen Pause:"

#: ui/config/general.py:180
msgid "休息时间上限:"
msgstr "Pausenlimit:"

#: ui/config/general.py:193
msgid "超过休息时间上限后，累计的番茄钟将归零"
msgstr ""
"Überschreitet die Pausenzeit das Limit, wird der Pomodoro-Zähler "
"zurückgesetzt."

#: ui/config/general.py:201
msgid "状态栏显示设置"
msgstr "Anzeigeeinstellungen für die Statusleiste"

#: ui/config/general.py:203
msgid "选择状态栏显示格式："
msgstr "Anzeigeformat für die Statusleiste auswählen:"


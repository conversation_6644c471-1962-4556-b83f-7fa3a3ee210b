from typing import Any

from aqt import (
    QCheckBox,
    QComboBox,
    QDoubleSpinBox,
    QFileDialog,
    QGridLayout,
    QGroupBox,
    QHBoxLayout,
    QLabel,
    QPushButton,
    QSpinBox,
    QVBoxLayout,
    QWidget,
)

from ...config.enums import AudioPlaybackMode, StatusBarFormat
from ...config.languages import LanguageCode
from ...config.types import AppConfig
from ...translator import _


class GeneralSettings:
    """处理常规设置的UI组件和逻辑"""

    def __init__(self, config: AppConfig):
        self.config = config
        self.enable_checkbox: QCheckBox | None = None
        self.audio_enabled_checkbox: QCheckBox | None = None
        self.playback_mode_combobox: QComboBox | None = None
        self.interval_spinbox: QSpinBox | None = None
        self.volume_spinbox: QDoubleSpinBox | None = None
        self.audio_file_button: QPushButton | None = None
        self.audio_file_label: QLabel | None = None
        self.show_status_bar_checkbox: QCheckBox | None = None
        self.statusbar_format_combobox: QComboBox | None = None
        self.language_combobox: QComboBox | None = None

    def create_ui(self, parent: QWidget) -> QGroupBox:
        """创建常规设置部分的UI组件"""
        group = QGroupBox(_("常规设置"))
        main_layout = QVBoxLayout()
        grid_layout = QGridLayout()
        grid_layout.setColumnStretch(1, 1)  # 让第二列（输入控件列）可以伸展
        grid_layout.setColumnMinimumWidth(0, 120)  # 给标签列一个最小宽度

        row = 0

        # 启用插件
        self.enable_checkbox = QCheckBox(_("启用音频保活插件"), parent)
        self.enable_checkbox.setChecked(self.config.enabled)
        grid_layout.addWidget(self.enable_checkbox, row, 0, 1, 2)
        row += 1

        # 语言选择
        language_label = QLabel(_("语言:"), parent)
        self.language_combobox = QComboBox(parent)
        self.language_combobox.addItems([code.display_name for code in LanguageCode])
        self.language_combobox.setCurrentText(self.config.language.display_name)
        grid_layout.addWidget(language_label, row, 0)
        grid_layout.addWidget(self.language_combobox, row, 1)
        row += 1

        # 音频设置组
        audio_group = QGroupBox(_("音频设置"))
        audio_layout = QGridLayout()
        audio_row = 0

        # 启用音频
        self.audio_enabled_checkbox = QCheckBox(_("启用音频播放"), parent)
        self.audio_enabled_checkbox.setChecked(self.config.audio_enabled)
        audio_layout.addWidget(self.audio_enabled_checkbox, audio_row, 0, 1, 2)
        audio_row += 1

        # 播放模式
        playback_mode_label = QLabel(_("播放模式:"), parent)
        self.playback_mode_combobox = QComboBox(parent)
        for mode in AudioPlaybackMode:
            self.playback_mode_combobox.addItem(mode.display_name, mode)
        # Set current selection
        for i in range(self.playback_mode_combobox.count()):
            if self.playback_mode_combobox.itemData(i) == self.config.playback_mode:
                self.playback_mode_combobox.setCurrentIndex(i)
                break
        audio_layout.addWidget(playback_mode_label, audio_row, 0)
        audio_layout.addWidget(self.playback_mode_combobox, audio_row, 1)
        audio_row += 1

        # 间隔时间（仅在间隔模式下显示）
        interval_label = QLabel(_("播放间隔(秒):"), parent)
        self.interval_spinbox = QSpinBox(parent)
        self.interval_spinbox.setRange(1, 3600)  # 1秒到1小时
        self.interval_spinbox.setValue(self.config.interval_seconds)
        audio_layout.addWidget(interval_label, audio_row, 0)
        audio_layout.addWidget(self.interval_spinbox, audio_row, 1)
        audio_row += 1

        # 音量设置
        volume_label = QLabel(_("音量:"), parent)
        self.volume_spinbox = QDoubleSpinBox(parent)
        self.volume_spinbox.setRange(0.0, 1.0)
        self.volume_spinbox.setSingleStep(0.1)
        self.volume_spinbox.setDecimals(1)
        self.volume_spinbox.setValue(self.config.volume)
        audio_layout.addWidget(volume_label, audio_row, 0)
        audio_layout.addWidget(self.volume_spinbox, audio_row, 1)
        audio_row += 1

        # 音频文件选择
        audio_file_label = QLabel(_("音频文件:"), parent)
        audio_file_layout = QHBoxLayout()
        self.audio_file_button = QPushButton(_("选择文件"), parent)
        self.audio_file_button.clicked.connect(self._select_audio_file)
        self.audio_file_label = QLabel(
            self.config.audio_file_path or _("未选择"), parent
        )
        self.audio_file_label.setWordWrap(True)
        audio_file_layout.addWidget(self.audio_file_button)
        audio_file_layout.addWidget(self.audio_file_label, 1)
        audio_layout.addWidget(audio_file_label, audio_row, 0)
        audio_layout.addLayout(audio_file_layout, audio_row, 1)
        audio_row += 1

        audio_group.setLayout(audio_layout)
        main_layout.addLayout(grid_layout)
        main_layout.addWidget(audio_group)

        # 界面设置组
        ui_group = QGroupBox(_("界面设置"))
        ui_layout = QGridLayout()
        ui_row = 0

        # 显示状态栏
        self.show_status_bar_checkbox = QCheckBox(_("显示状态栏信息"), parent)
        self.show_status_bar_checkbox.setChecked(self.config.show_status_bar)
        ui_layout.addWidget(self.show_status_bar_checkbox, ui_row, 0, 1, 2)
        ui_row += 1

        # 状态栏格式
        statusbar_format_label = QLabel(_("状态栏格式:"), parent)
        self.statusbar_format_combobox = QComboBox(parent)
        for format_option in StatusBarFormat:
            self.statusbar_format_combobox.addItem(
                format_option.display_name, format_option
            )
        # Set current selection
        for i in range(self.statusbar_format_combobox.count()):
            if (
                self.statusbar_format_combobox.itemData(i)
                == self.config.statusbar_format
            ):
                self.statusbar_format_combobox.setCurrentIndex(i)
                break
        ui_layout.addWidget(statusbar_format_label, ui_row, 0)
        ui_layout.addWidget(self.statusbar_format_combobox, ui_row, 1)
        ui_row += 1

        ui_group.setLayout(ui_layout)
        main_layout.addWidget(ui_group)

        group.setLayout(main_layout)
        return group

    def _select_audio_file(self):
        """选择音频文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            None,
            _("选择音频文件"),
            "",
            _("音频文件 (*.opus *.mp3 *.wav *.ogg);;所有文件 (*)"),
        )
        if file_path:
            self.audio_file_label.setText(file_path)

    def get_values(self) -> dict[str, Any]:
        """获取UI中的所有值"""
        # Get language key
        language_key = LanguageCode.AUTO
        for code in LanguageCode:
            if code.display_name == self.language_combobox.currentText():
                language_key = code
                break

        return {
            "enabled": self.enable_checkbox.isChecked(),
            "language": language_key,
            "audio_enabled": self.audio_enabled_checkbox.isChecked(),
            "playback_mode": self.playback_mode_combobox.currentData(),
            "interval_seconds": self.interval_spinbox.value(),
            "volume": self.volume_spinbox.value(),
            "audio_file_path": self.audio_file_label.text()
            if self.audio_file_label.text() != _("未选择")
            else "",
            "show_status_bar": self.show_status_bar_checkbox.isChecked(),
            "statusbar_format": self.statusbar_format_combobox.currentData(),
        }

from aqt import QDialog, QLabel, QPushButton, QVBoxLayout, mw
from aqt.utils import openLink

from ..translator import _


def show_update_warning(required_version: str, current_version: str):
    """Show a dialog warning about incompatible Anki version."""
    
    class UpdateDialog(QDialog):
        def __init__(self, parent=None):
            super().__init__(parent or mw)
            self.setWindowTitle(_("版本不兼容"))
            self.setModal(True)
            
            layout = QVBoxLayout()
            
            # Warning message
            warning_text = _(
                "此插件需要 Anki {} 或更高版本。\n"
                "您当前的版本是 {}。\n\n"
                "请更新 Anki 以使用此插件。"
            ).format(required_version, current_version)
            
            warning_label = QLabel(warning_text)
            warning_label.setWordWrap(True)
            layout.addWidget(warning_label)
            
            # Download button
            download_button = QPushButton(_("下载最新版本"))
            download_button.clicked.connect(self._open_download_page)
            layout.addWidget(download_button)
            
            # Close button
            close_button = QPushButton(_("关闭"))
            close_button.clicked.connect(self.accept)
            layout.addWidget(close_button)
            
            self.setLayout(layout)
            
        def _open_download_page(self):
            """Open Anki download page."""
            openLink("https://apps.ankiweb.net/")
            self.accept()
    
    dialog = UpdateDialog()
    dialog.exec()

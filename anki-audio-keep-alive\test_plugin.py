#!/usr/bin/env python3
"""
Simple test script to verify the plugin structure and basic functionality.
This script checks that all modules can be imported and basic configuration works.
"""

import sys
from pathlib import Path

# Add the src directory to the Python path
src_dir = Path(__file__).parent / "src"
sys.path.insert(0, str(src_dir))


def test_imports():
    """Test that all modules can be imported without errors."""
    print("Testing module imports...")

    try:
        # Test basic Python syntax and structure
        print("✓ Testing basic Python syntax and structure...")

        # Check that files can be read and have basic Python syntax
        config_files = [
            "config/enums.py",
            "config/languages.py",
            "config/constants.py",
            "config/types.py",
            "config/config.py",
            "audio_manager.py",
            "audio_player.py",
            "state.py",
        ]

        for config_file in config_files:
            file_path = Path(__file__).parent / "src" / config_file
            if file_path.exists():
                with open(file_path, "r", encoding="utf-8") as f:
                    content = f.read()
                    # Basic syntax check - try to compile
                    compile(content, str(file_path), "exec")
                print(f"✓ {config_file} - syntax OK")
            else:
                print(f"✗ {config_file} - file not found")
                return False

        print("✓ All Python files have valid syntax")

        # Note about runtime testing
        print("⚠ Full import testing requires Anki environment")
        print("  The plugin structure is correct and should work in Anki")

        return True

    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        return False


def test_file_structure():
    """Test that all required files exist."""
    print("\nTesting file structure...")

    required_files = [
        "src/__init__.py",
        "src/audio_manager.py",
        "src/audio_player.py",
        "src/hooks.py",
        "src/state.py",
        "src/translator.py",
        "src/config/__init__.py",
        "src/config/config.py",
        "src/config/types.py",
        "src/config/enums.py",
        "src/ui/__init__.py",
        "src/ui/config/dialog.py",
        "src/ui/config/general.py",
        "src/locales/messages.pot",
        "src/locales/en_US/LC_MESSAGES/messages.po",
        "src/media/silent.opus",
        "src/babel.cfg",
        "src/meta.json",
        "pyproject.toml",
        "README.md",
    ]

    missing_files = []
    for file_path in required_files:
        full_path = Path(__file__).parent / file_path
        if not full_path.exists():
            missing_files.append(file_path)
        else:
            print(f"✓ {file_path}")

    if missing_files:
        print(f"\n✗ Missing files: {missing_files}")
        return False
    else:
        print("\n✓ All required files present")
        return True


def test_audio_file():
    """Test that the audio file exists and is accessible."""
    print("\nTesting audio file...")

    audio_file = Path(__file__).parent / "src" / "media" / "silent.opus"
    if audio_file.exists():
        size = audio_file.stat().st_size
        print(f"✓ Silent audio file exists: {audio_file} ({size} bytes)")
        return True
    else:
        print(f"✗ Silent audio file not found: {audio_file}")
        return False


def main():
    """Run all tests."""
    print("Anki Audio Keep-Alive Plugin - Structure Test")
    print("=" * 50)

    tests = [
        test_file_structure,
        test_audio_file,
        test_imports,
    ]

    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
            results.append(False)

    print("\n" + "=" * 50)
    passed = sum(results)
    total = len(results)

    if passed == total:
        print(f"✓ All {total} tests passed!")
        print("\nThe plugin structure appears to be correct.")
        print("You can now install this plugin in Anki for testing.")
    else:
        print(f"✗ {total - passed} out of {total} tests failed.")
        print("\nPlease fix the issues before installing the plugin.")

    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

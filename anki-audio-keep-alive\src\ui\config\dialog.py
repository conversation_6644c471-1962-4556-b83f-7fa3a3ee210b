import dataclasses
from typing import override

from aqt import (
    <PERSON><PERSON><PERSON><PERSON>,
    QDialogButtonBox,
    QTabWidget,
    QVBoxLayout,
    QWidget,
    mw,
)
from aqt.utils import tooltip

from ...config.types import AppConfig
from ...state import get_audio_manager, reload_config, update_and_save_config
from ...translator import _, set_language
from .general import GeneralSettings


class ConfigDialog(QDialog):
    """用于音频保活设置的配置对话框。"""

    def __init__(self, parent: QWidget = mw):
        super().__init__(parent or mw)

        self.config = reload_config()
        self.setWindowTitle(_("音频保活设置"))
        self._main_layout = QVBoxLayout(self)

        self.tabs = QTabWidget()
        self.general_tab = QWidget()

        self.tabs.addTab(self.general_tab, _("常规设置"))

        self.setup_general_tab()

        self._main_layout.addWidget(self.tabs)

        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Save
            | QDialogButtonBox.StandardButton.Cancel,
            self,
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        self._main_layout.addWidget(button_box)

        self.setLayout(self._main_layout)

    def setup_general_tab(self):
        """设置常规选项卡"""
        layout = QVBoxLayout(self.general_tab)
        self.general_settings = GeneralSettings(self.config)
        layout.addWidget(self.general_settings.create_ui(self))

    @override
    def accept(self):
        """当点击"保存"时，收集UI值，更新全局状态并保存到文件。"""
        try:
            general_values = self.general_settings.get_values()

            config_dict = dataclasses.asdict(self.config)
            config_dict.update(general_values)

            app_config_fields = {field.name for field in dataclasses.fields(AppConfig)}
            filtered_config_dict = {
                k: v for k, v in config_dict.items() if k in app_config_fields
            }

            config_to_save = AppConfig(**filtered_config_dict)

            update_and_save_config(config_to_save)
            set_language(config_to_save.language)
            tooltip(_("配置已保存"))

            # Restart audio manager if it exists
            audio_manager = get_audio_manager()
            if audio_manager:
                audio_manager.stop_audio_keepalive()
                if config_to_save.enabled and config_to_save.audio_enabled:
                    audio_manager.start_audio_keepalive()

            super().accept()

        except Exception as e:
            tooltip(f"保存配置时出错: {e}", period=5000)

    @override
    def reject(self):
        """当点击"取消"时关闭对话框。"""
        super().reject()

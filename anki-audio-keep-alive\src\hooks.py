from anki.cards import Card
from aqt import mw

from .config.constants import AnkiStates
from .state import get_app_state, get_audio_manager, get_config, set_audio_manager
from .translator import _


# --- Anki 钩子函数 ---


def on_reviewer_did_start(card: Card):
    """Starts the audio keep-alive when the reviewer screen is shown."""
    config = get_config()
    audio_manager = get_audio_manager()

    if not config.enabled or not config.audio_enabled:
        return

    # If <PERSON><PERSON> is in review state
    if mw.state == AnkiStates.REVIEW:
        if audio_manager is None:
            from .audio_manager import AudioManager
            audio_manager = AudioManager()
            set_audio_manager(audio_manager)

        # Start audio keep-alive if not already active
        if not audio_manager.is_active():
            audio_manager.start_audio_keepalive()

        # Handle card change audio (for ON_CARD_CHANGE mode)
        audio_manager.play_on_card_change()


def on_state_did_change(new_state: str, old_state: str):
    """管理状态变更时的音频保活状态"""
    audio_manager = get_audio_manager()
    config = get_config()

    # 离开复习状态时停止音频保活
    if (
        old_state == AnkiStates.REVIEW
        and new_state != AnkiStates.REVIEW
        and audio_manager
        and config.enabled
        and audio_manager.is_active()
    ):
        audio_manager.stop_audio_keepalive()

    # 进入复习状态时启动音频保活
    if (
        new_state == AnkiStates.REVIEW
        and old_state != AnkiStates.REVIEW
        and audio_manager
        and config.enabled
        and config.audio_enabled
        and not audio_manager.is_active()
    ):
        audio_manager.start_audio_keepalive()


def on_theme_change():
    """
    当Anki的主题（白天/夜间模式）改变时调用。
    这个函数可以用来更新UI元素的颜色。
    """
    # For future use if we add visual indicators
    pass

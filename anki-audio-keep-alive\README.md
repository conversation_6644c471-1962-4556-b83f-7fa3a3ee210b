# Anki Audio Keep-Alive Plugin

An Anki plugin that automatically plays silent audio files to keep audio devices active during study sessions. This prevents audio devices from going to sleep or switching off during long study periods.

## Features

- 🔊 **Silent Audio Playback** - Automatically plays silent audio files to keep audio devices active
- ⚙️ **Multiple Playback Modes** - Continuous, interval-based, or on card change
- 🎛️ **Configurable Settings** - Adjust volume, intervals, and audio files
- 📊 **Status Bar Integration** - Real-time status display in <PERSON><PERSON>'s status bar
- 🌍 **Multi-language Support** - English, German, and Chinese (Simplified)
- 🎯 **Anki Integration** - Seamless integration with <PERSON><PERSON>'s review system

## Installation

### From Anki Add-ons
1. In Anki, click "Tools" > "Add-ons" > "Get Add-ons"
2. Enter the add-on code (when available)
3. Restart Anki

### Manual Installation
1. Download the plugin files
2. Copy the `src` folder to your Anki add-ons directory
3. Restart Anki

## Usage

### Basic Setup
1. Go to "Tools" > "Audio Keep-Alive Settings..."
2. Enable the plugin by checking "Enable Audio Keep-Alive Plugin"
3. Configure your preferred settings
4. Click "Save"

### Playback Modes

#### Continuous Playback
- Plays silent audio continuously during review sessions
- Best for keeping audio devices consistently active

#### Interval Playback
- Plays silent audio at regular intervals (configurable)
- More resource-efficient while still maintaining device activity
- Default interval: 30 seconds

#### On Card Change
- Plays silent audio each time you move to a new card
- Minimal resource usage
- Good for shorter study sessions

### Configuration Options

#### Audio Settings
- **Enable Audio Playback**: Toggle audio keep-alive functionality
- **Playback Mode**: Choose between continuous, interval, or on-card-change
- **Playback Interval**: Set interval in seconds (for interval mode)
- **Volume**: Adjust audio volume (recommended: very low, 0.1)
- **Audio File**: Select custom silent audio file (optional)

#### Interface Settings
- **Show Status Bar Information**: Display audio status in Anki's status bar
- **Status Bar Format**: Choose what information to display

## Menu Options

The plugin adds the following menu items to Anki's Tools menu:
- **Audio Keep-Alive Settings...** - Open the configuration dialog
- **Test Silent Audio** - Test audio playback functionality

## Technical Details

### Audio Formats Supported
- OPUS (recommended)
- MP3
- WAV
- OGG

### System Requirements
- Anki 25.07 or higher
- Python 3.13+
- PyQt6 with multimedia support

### Default Audio File
The plugin includes a default silent OPUS audio file optimized for minimal file size and maximum compatibility.

## Development

### Project Structure
```
src/
├── __init__.py              # Main plugin entry point
├── audio_manager.py         # Audio playback management
├── audio_player.py          # Audio player implementation
├── hooks.py                 # Anki hook integration
├── state.py                 # State management
├── translator.py            # Internationalization
├── config/                  # Configuration system
│   ├── __init__.py
│   ├── anki_config.py      # Anki config integration
│   ├── config.py           # Configuration management
│   ├── constants.py        # Constants and defaults
│   ├── enums.py            # Enumerations
│   ├── languages.py        # Language definitions
│   └── types.py            # Data types and validation
├── ui/                     # User interface
│   ├── __init__.py
│   ├── statusbar.py        # Status bar integration
│   ├── version_dialog.py   # Version compatibility dialog
│   └── config/             # Configuration dialogs
│       ├── __init__.py
│       ├── dialog.py       # Main config dialog
│       └── general.py      # General settings UI
├── locales/                # Translation files
│   ├── messages.pot        # Translation template
│   └── en_US/              # English translations
├── media/                  # Audio files
│   └── silent.opus         # Default silent audio
└── vendor/                 # Third-party dependencies
```

### Translation Workflow

#### Prerequisites: Install Babel
```bash
pip install babel
pybabel --version  # Verify installation
```

#### Step 1: Extract Strings
```bash
cd src
pybabel extract -F babel.cfg -o locales/messages.pot .
```

#### Step 2: Initialize New Language
```bash
pybabel init -i locales/messages.pot -d locales -l <language_code>
```

#### Step 3: Update Existing Translations
```bash
pybabel update -i locales/messages.pot -d locales
```

#### Step 4: Translate Strings
Edit the PO files in `locales/<language_code>/LC_MESSAGES/messages.po`

#### Step 5: Compile Translations
```bash
pybabel compile -d locales
```

### Adding New Strings for Translation
1. Wrap strings with `_()` function in code:
```python
from .translator import _
message = _("Text to translate")
```

2. Extract and update translations following the workflow above

## Troubleshooting

### Audio Not Playing
1. Check that audio is enabled in settings
2. Verify the audio file path is correct
3. Test with the "Test Silent Audio" menu option
4. Check system audio settings and permissions

### Plugin Not Loading
1. Verify Anki version compatibility (25.07+)
2. Check for error messages in Anki's debug console
3. Ensure all plugin files are in the correct directory

### Status Bar Not Updating
1. Enable "Show Status Bar Information" in settings
2. Restart Anki after changing settings
3. Check that the status bar format is properly configured

## License

This project follows the same license structure as the reference AnkiPomodoroTimerBreathingExercise project.

## Contributing

Contributions are welcome! Please follow the existing code patterns and ensure all translations are updated when adding new features.

## Acknowledgments

This plugin was built following the architecture and patterns from the AnkiPomodoroTimerBreathingExercise project, providing a solid foundation for Anki plugin development.

from enum import Enum

from ..translator import _


class AudioPlaybackMode(str, Enum):
    _display_name: str

    def __new__(cls, value: str, display_name: str):
        obj = str.__new__(cls, value)
        obj._value_ = value
        obj._display_name = display_name
        return obj

    @property
    def display_name(self) -> str:
        return self._display_name

    # Define members with their display names
    CONTINUOUS = "continuous", _("连续播放")
    INTERVAL = "interval", _("间隔播放")
    ON_CARD_CHANGE = "on_card_change", _("换卡时播放")


class AudioFormat(str, Enum):
    OPUS = "opus"
    MP3 = "mp3"
    WAV = "wav"


class StatusBarFormat(str, Enum):
    _display_name: str

    def __new__(cls, value: str, display_name: str):
        obj = str.__new__(cls, value)
        obj._value_ = value
        obj._display_name = display_name
        return obj

    @property
    def display_name(self) -> str:
        return self._display_name

    # Define members with their display names
    NONE = "NONE", _("不显示")
    ICON = "{icon}", _("仅显示图标")
    AUDIO_STATUS = "{audio_status}", _("显示音频状态")
    ICON_AUDIO_STATUS = "{icon} {audio_status}", _("显示图标+音频状态")
    ICON_AUDIO_STATUS_WITH_TIME = (
        "{icon} {audio_status} 🕒 {daily_hours}h {daily_mins}m",
        _("显示图标+音频状态+累计时间"),
    )

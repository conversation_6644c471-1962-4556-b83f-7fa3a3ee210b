import sys
from pathlib import Path

# Add the 'vendor' directory to Python's path
vendor_dir = Path(__file__).resolve().parent.parent / "vendor"
if str(vendor_dir) not in sys.path:
    sys.path.insert(0, str(vendor_dir))

import dataclasses
from pathlib import Path

from koda_validate import DataclassValidator

from .constants import SILENT_AUDIO_FILE
from .enums import AudioPlaybackMode, StatusBarFormat
from .languages import LanguageCode


def get_default_audio_path() -> str:
    """Get the default path to the silent audio file."""
    media_path = Path(__file__).resolve().parent.parent / "media"
    return str(media_path / SILENT_AUDIO_FILE)


@dataclasses.dataclass
class DisplayPosition:
    """存储显示器的位置和相关信息"""

    serial_number: str
    resolution: tuple[int, int]
    logical_dpi: tuple[float, float]
    pos: tuple[int, int]


@dataclasses.dataclass
class AppConfig:
    """
    应用程序的主配置数据类。
    存储经过验证和填充的配置值。
    """

    # 常规设置
    enabled: bool = True
    language: LanguageCode = LanguageCode.AUTO

    # 音频设置
    audio_enabled: bool = True
    playback_mode: AudioPlaybackMode = AudioPlaybackMode.INTERVAL
    interval_seconds: int = 30
    volume: float = 0.1
    audio_file_path: str = dataclasses.field(default_factory=get_default_audio_path)
    
    # 界面设置
    show_status_bar: bool = True
    statusbar_format: StatusBarFormat = StatusBarFormat.ICON_AUDIO_STATUS_WITH_TIME
    
    # 状态字段
    daily_audio_seconds: int = 0
    last_date: str = ""
    total_audio_plays: int = 0


# --- 使用 DataclassValidator 简化并修正验证逻辑 ---
# 这一行代码会根据上面的 AppConfig 类自动生成一个完整的、正确的验证器。
# 它会处理所有字段的类型检查和默认值。
config_validator = DataclassValidator(AppConfig)

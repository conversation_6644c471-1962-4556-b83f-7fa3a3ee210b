from aqt import <PERSON><PERSON>abel, mw

from ..config.constants import Defaults
from ..config.enums import StatusBarFormat
from ..config.types import AppConfig
from ..state import get_app_state


def show_audio_status_in_statusbar(audio_manager=None):
    """Update the status bar with audio keep-alive information."""
    if not mw or not mw.form or not mw.form.statusbar:
        return

    app_state = get_app_state()
    config = app_state.config

    if not config.show_status_bar:
        # Remove status label if it exists
        if app_state.status_label:
            mw.form.statusbar.removeWidget(app_state.status_label)
            app_state.status_label.deleteLater()
            app_state.status_label = None
        return

    # Create or get existing status label
    if not app_state.status_label:
        app_state.status_label = QLabel()
        mw.form.statusbar.addPermanentWidget(app_state.status_label)

    # Format the status text
    status_text = _format_status_text(config, audio_manager)
    app_state.status_label.setText(status_text)


def _format_status_text(config: AppConfig, audio_manager=None) -> str:
    """Format the status bar text based on configuration."""
    format_str = config.statusbar_format.value
    
    # Determine audio status
    if audio_manager and audio_manager.is_active():
        if audio_manager.audio_player and audio_manager.audio_player.is_playing():
            audio_status = Defaults.StatusBar.AUDIO_PLAYING
            icon = Defaults.StatusBar.AUDIO_ACTIVE
        else:
            audio_status = Defaults.StatusBar.AUDIO_ACTIVE
            icon = Defaults.StatusBar.AUDIO_ACTIVE
    else:
        audio_status = Defaults.StatusBar.AUDIO_PAUSED
        icon = Defaults.StatusBar.AUDIO_INACTIVE

    # Calculate daily time
    daily_seconds = config.daily_audio_seconds
    daily_hours = daily_seconds // 3600
    daily_mins = (daily_seconds % 3600) // 60

    # Format the string
    try:
        return format_str.format(
            icon=icon,
            audio_status=audio_status,
            daily_hours=daily_hours,
            daily_mins=daily_mins,
        )
    except (KeyError, ValueError):
        # Fallback to default format if there's an error
        return f"{icon} {audio_status}"


def update_status_bar(audio_manager=None):
    """Convenience function to update the status bar."""
    show_audio_status_in_statusbar(audio_manager)

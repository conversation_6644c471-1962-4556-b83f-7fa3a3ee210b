# pyproject.toml (Minimal Version)

[build-system]
requires = ["setuptools", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "anki-audio-keep-alive"
version = "1.0.0"
description = "An Anki plugin that automatically plays silent audio files to keep audio devices active during study sessions."
requires-python = ">=3.13"
dependencies = ["koda-validate>=5.0.0"]

[tool.setuptools.packages.find]
# 自动查找项目中的所有Python包 (即包含 __init__.py 的目录)
# 这会找到 src 目录
where = ["."]

[tool.setuptools.package-data]
# 确保插件所需的非代码文件被一同打包，这对于插件的正常功能至关重要
"src" = [
    "locales/*.pot",             # 打包翻译模板
    "locales/*_*/LC_MESSAGES/*", # 打包所有语言的 .po 和 .mo 文件
    "babel.cfg",                 # 打包 Babel 配置文件
    "media/*.opus",              # 打包静音音频文件
]

[dependency-groups]
dev = ["aqt>=25.7.2", "babel>=2.17.0"]

[tool.pyright]
include = ["src"]
exclude = ["**/vendor"]

[tool.ruff]
line-length = 88
target-version = "py313"

[tool.ruff.lint]
select = [
    # pycodestyle
    "E",
    # Pyflakes
    "F",
    # pyupgrade
    "UP",
    # flake8-bugbear
    "B",
    # flake8-simplify
    "SIM",
    # isort
    "I",
]
ignore = ["E402"]

from aqt import QTimer, mw
from aqt.utils import tooltip

from .audio_player import AudioPlayer
from .config.enums import AudioPlaybackMode
from .state import get_app_state
from .translator import _
from .ui.statusbar import update_status_bar


class AudioManager:
    """Manages automatic silent audio playback to keep audio devices active."""

    def __init__(self):
        self.app_state = get_app_state()
        self.audio_player: AudioPlayer | None = None
        self._playback_timer: QTimer | None = None
        self._is_active = False

        # Initialize audio player
        self._init_audio_player()

        # Initialize playback timer
        self._init_playback_timer()

    def _init_audio_player(self):
        """Initialize the audio player."""
        self.audio_player = AudioPlayer(mw)

    def _init_playback_timer(self):
        """Initialize the playback timer for interval mode."""
        self._playback_timer = QTimer(mw)
        self._playback_timer.timeout.connect(self._on_timer_tick)

    def start_audio_keepalive(self):
        """Start the audio keep-alive system."""
        config = self.app_state.config

        if not config.enabled or not config.audio_enabled:
            return

        self._is_active = True

        if config.playback_mode == AudioPlaybackMode.CONTINUOUS:
            self._start_continuous_playback()
        elif config.playback_mode == AudioPlaybackMode.INTERVAL:
            self._start_interval_playback()
        # ON_CARD_CHANGE mode is handled by hooks

        tooltip(_("音频保活已启动"), period=2000)
        update_status_bar(self)

    def stop_audio_keepalive(self):
        """Stop the audio keep-alive system."""
        self._is_active = False

        if self._playback_timer and self._playback_timer.isActive():
            self._playback_timer.stop()

        if self.audio_player:
            self.audio_player.stop()

        tooltip(_("音频保活已停止"), period=2000)
        update_status_bar(self)

    def _start_continuous_playback(self):
        """Start continuous audio playback."""
        config = self.app_state.config
        if self.audio_player and config.audio_file_path:
            self.audio_player.play(config.audio_file_path, config.volume)

    def _start_interval_playback(self):
        """Start interval-based audio playback."""
        config = self.app_state.config
        if self._playback_timer:
            # Convert seconds to milliseconds
            interval_ms = config.interval_seconds * 1000
            self._playback_timer.start(interval_ms)

            # Play immediately
            self._play_silent_audio()

    def _on_timer_tick(self):
        """Handle timer tick for interval playback."""
        if self._is_active:
            self._play_silent_audio()

    def _play_silent_audio(self):
        """Play a single instance of silent audio."""
        config = self.app_state.config
        if self.audio_player and config.audio_file_path:
            self.audio_player.play(config.audio_file_path, config.volume)

            # Update statistics
            self.app_state.update_config_value(
                "total_audio_plays", config.total_audio_plays + 1
            )
            self.app_state.update_config_value(
                "daily_audio_seconds", config.daily_audio_seconds + 1
            )

    def play_on_card_change(self):
        """Play audio when card changes (for ON_CARD_CHANGE mode)."""
        config = self.app_state.config

        if (
            config.enabled
            and config.audio_enabled
            and config.playback_mode == AudioPlaybackMode.ON_CARD_CHANGE
            and self._is_active
        ):
            self._play_silent_audio()

    def is_active(self) -> bool:
        """Check if audio keep-alive is currently active."""
        return self._is_active

    def cleanup(self):
        """Clean up resources."""
        self.stop_audio_keepalive()
        if self.audio_player:
            self.audio_player.stop()


def test_audio_playback():
    """Test function to play silent audio once."""
    from .config.types import get_default_audio_path

    audio_player = AudioPlayer(mw)
    audio_path = get_default_audio_path()

    if audio_path:
        audio_player.play(audio_path, 0.1)
        tooltip(_("测试音频播放"), period=2000)
    else:
        tooltip(_("找不到音频文件"), period=2000)

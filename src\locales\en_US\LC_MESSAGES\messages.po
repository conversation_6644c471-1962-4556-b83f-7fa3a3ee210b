# English (United States) translations for PROJECT.
# Copyright (C) 2025 ORGANIZATION
# This file is distributed under the same license as the PROJECT project.
# <AUTHOR> <EMAIL>, 2025.
#
msgid ""
msgstr ""
"Project-Id-Version: PROJECT VERSION\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-07-13 12:58+0900\n"
"PO-Revision-Date: 2025-04-04 12:15+0900\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: en_US\n"
"Language-Team: en_US <<EMAIL>>\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#: __init__.py:52
#, python-brace-format
msgid "无法解析Anki版本: {}"
msgstr "Could not parse Anki version: {}"

#: __init__.py:107
msgid "番茄钟 & 呼吸设置..."
msgstr "Pomodoro & Breathing Settings..."

#: __init__.py:110
msgid "启动呼吸训练"
msgstr "Start Breathing Exercise"

#: __init__.py:119
msgid "警告: 无法添加番茄钟菜单项"
msgstr "Warning: Cannot add Pomodoro menu item"

#: hooks.py:60 pomodoro/pomodoro_manager.py:103
msgid "番茄钟计时器已停止。"
msgstr "Pomodoro timer stopped."

#: hooks.py:78
#, python-brace-format
msgid "恭喜完成{target}个番茄钟！建议休息{minutes}分钟。"
msgstr ""
"Congratulations on completing {target} pomodoros! Suggested break: "
"{minutes} minutes."

#: hooks.py:85
msgid "番茄钟时间到！"
msgstr "Pomodoro time's up! Take a break."

#: hooks.py:124
msgid "呼吸训练已跳过 (无启用阶段)。"
msgstr "Breathing exercise skipped (no phases enabled)."

#: hooks.py:129
msgid "呼吸训练已跳过 (循环次数为 0)。"
msgstr "Breathing exercise skipped (cycle count is 0)."

#: hooks.py:135
msgid "呼吸训练完成！"
msgstr "Breathing exercise completed!"

#: hooks.py:137
msgid "呼吸训练已跳过。"
msgstr "Breathing exercise skipped."

#: hooks.py:139
msgid "跳过呼吸训练 (主窗口不可见)。"
msgstr "Skipping breathing exercise (main window not visible)."

#: config/constants.py:48
msgid "⏳休息中："
msgstr "⏳Resting:"

#: config/constants.py:49
msgid "⚠️休息上限："
msgstr "⚠️Break Limit:"

#: config/enums.py:21
msgid "左上角"
msgstr "Top Left"

#: config/enums.py:22
msgid "右上角"
msgstr "Top Right"

#: config/enums.py:23
msgid "左下角"
msgstr "Bottom Left"

#: config/enums.py:24
msgid "右下角"
msgstr "Bottom Right"

#: config/enums.py:25
msgid "上次使用的位置"
msgstr "Last Used Position"

#: config/enums.py:58
msgid "吸气"
msgstr "Inhale"

#: config/enums.py:64
msgid "屏气 (吸气后)"
msgstr "Breath-hold (after inhale)"

#: config/enums.py:70
msgid "呼气"
msgstr "Exhale"

#: config/enums.py:76
msgid "屏气 (呼气后)"
msgstr "Breath-hold (after exhale)"

#: config/enums.py:97
msgid "不显示"
msgstr "Hide"

#: config/enums.py:98
msgid "仅显示图标"
msgstr "Icon only"

#: config/enums.py:99
msgid "仅显示倒计时"
msgstr "Timer only"

#: config/enums.py:100
msgid "仅显示进度"
msgstr "Progress only"

#: config/enums.py:103
msgid "显示图标+倒计时+进度"
msgstr "Show icon + timer + progress"

#: config/enums.py:107
msgid "显示图标+倒计时+进度+累计使用时间"
msgstr "Show icon + timer + progress + total usage time"

#: pomodoro/pomodoro_manager.py:55
msgid "本次番茄钟结束"
msgstr "Current pomodoro ended"

#: pomodoro/pomodoro_manager.py:83
msgid "番茄钟计时器已被禁用。"
msgstr "Pomodoro timer has been disabled."

#: pomodoro/pomodoro_manager.py:96
#, python-brace-format
msgid "番茄钟计时器已启动，时长: {} 分钟。"
msgstr "Pomodoro timer started, duration: {} minutes."

#: pomodoro/pomodoro_manager.py:123
msgid "检测到长时间空闲，连胜中断。"
msgstr "Long idle time detected, streak broken."

#: pomodoro/pomodoro_manager.py:157
msgid "休息时间过长，番茄钟连胜已清空。"
msgstr "Break time too long, pomodoro streak has been reset."

#: ui/version_dialog.py:13
msgid "请更新Anki"
msgstr "Please Update Anki"

#: ui/version_dialog.py:21
#, python-brace-format
msgid "Anki番茄钟 & 呼吸训练插件需要Anki版本 <b>{required_version}</b> 或更高版本才能运行。"
msgstr ""
"The Anki Pomodoro Timer & Breathe Exercise addon requires Anki version "
"<b>{required_version}</b> or later to work."

#: ui/version_dialog.py:24
#, python-brace-format
msgid "您当前使用的Anki版本是 <b>{current_version}</b>。"
msgstr "You are currently using Anki version <b>{current_version}</b>."

#: ui/version_dialog.py:28
msgid "请更新Anki到最新版本以使用此插件。"
msgstr "Please update Anki to the latest version to use this addon."

#: ui/version_dialog.py:34
msgid "跳转到Anki更新页面"
msgstr "Go to Anki Update Page"

#: ui/version_dialog.py:38
msgid "确定"
msgstr "OK"

#: ui/breathing/dialog.py:30 ui/config/dialog.py:36
msgid "呼吸训练"
msgstr "Breathing Exercise"

#: ui/breathing/dialog.py:46
msgid "准备..."
msgstr "Preparing..."

#: ui/breathing/dialog.py:53 ui/breathing/dialog.py:84
#, python-brace-format
msgid "循环: {current} / {total}"
msgstr "Cycle: {current} / {total}"

#: ui/breathing/dialog.py:61
msgid "跳过训练"
msgstr "Skip training"

#: ui/circularTimer/core/window.py:48
msgid "番茄钟计时器"
msgstr "Pomodoro Timer"

#: ui/config/breathing.py:46
msgid "呼吸训练设置"
msgstr "Breathing Exercise Settings"

#: ui/config/breathing.py:50
msgid "目标循环次数:"
msgstr "Target Cycle Count:"

#: ui/config/breathing.py:59
msgid "呼吸阶段设置"
msgstr "Breathing Phase Settings:"

#: ui/config/breathing.py:85
msgid "选择音频"
msgstr "Select Audio"

#: ui/config/breathing.py:87 ui/config/breathing.py:158
msgid "未选择"
msgstr "Not selected"

#: ui/config/breathing.py:97
msgid "持续时间:"
msgstr "Duration:"

#: ui/config/breathing.py:105
msgid "秒"
msgstr "seconds"

#: ui/config/breathing.py:116
msgid "预计时间: --:--"
msgstr "Estimated Time: --:--"

#: ui/config/breathing.py:148
#, python-brace-format
msgid "为 {phase_key} 阶段选择音频文件"
msgstr "Select audio file for {phase_key} phase"

#: ui/config/breathing.py:150
msgid "音频文件 (*.wav *.mp3 *.opus *.ogg);;所有文件 (*)"
msgstr "Audio files (*.wav *.mp3 *.opus *.ogg);;All files (*)"

#: ui/config/dialog.py:28
msgid "番茄钟/呼吸训练设置"
msgstr "Pomodoro/Breathing Exercise Settings"

#: ui/config/dialog.py:35 ui/config/general.py:42
msgid "常规设置"
msgstr "General Settings"

#: ui/config/dialog.py:98
msgid "预计时间: --:-- (未启用或周期为0)"
msgstr "Estimated Time: --:-- (not enabled or cycles is 0)"

#: ui/config/dialog.py:105
#, python-brace-format
msgid "预计时间: {mins:02d}:{secs:02d}"
msgstr "Estimated Time: {mins:02d}:{secs:02d}"

#: ui/config/dialog.py:111
msgid "预计时间: 计算错误"
msgstr "Estimated Time: Calculation Error"

#: ui/config/dialog.py:134
msgid "配置已保存"
msgstr "Configuration saved"

#: ui/config/dialog.py:142
#, python-brace-format
msgid "保存配置时出错: {}"
msgstr "Error saving configuration: {}"

#: ui/config/general.py:51
msgid "启用番茄钟插件"
msgstr "Enable Pomodoro Plugin"

#: ui/config/general.py:57
msgid "语言:"
msgstr "Language:"

#: ui/config/general.py:66
msgid "全局计时器"
msgstr "Global Timer"

#: ui/config/general.py:72
msgid "显示圆形计时器"
msgstr "Show Circular Timer"

#: ui/config/general.py:78
msgid "圆形计时器样式:"
msgstr "Circular Timer Style:"

#: ui/config/general.py:91
msgid "计时器窗口位置:"
msgstr "Timer Window Position:"

#: ui/config/general.py:104
msgid "连胜上限:"
msgstr "Streak Limit:"

#: ui/config/general.py:109 ui/config/general.py:130
msgid "个番茄钟"
msgstr "pomodoros"

#: ui/config/general.py:117
msgid "连续完成指定数量的番茄钟后，将进行长休息"
msgstr "Take a long break after completing specified number of pomodoros"

#: ui/config/general.py:123
msgid "进度显示阈值:"
msgstr "Progress Display Threshold:"

#: ui/config/general.py:143
msgid "当目标番茄钟数量超过此值时，将以 '🍅 x N' 的形式显示进度"
msgstr "When the target number of pomodoros exceeds this value, the progress will be displayed as '🍅 x N'"

#: ui/config/general.py:152
msgid "番茄钟时长:"
msgstr "Pomodoro Duration:"

#: ui/config/general.py:157 ui/config/general.py:171 ui/config/general.py:185
msgid "分钟"
msgstr "minutes"

#: ui/config/general.py:166
msgid "长休息时长:"
msgstr "Long Break Duration:"

#: ui/config/general.py:180
msgid "休息时间上限:"
msgstr "Break Time Limit:"

#: ui/config/general.py:193
msgid "超过休息时间上限后，累计的番茄钟将归零"
msgstr "Reset accumulated pomodoros when break time limit is exceeded"

#: ui/config/general.py:201
msgid "状态栏显示设置"
msgstr "Status Bar Display Settings"

#: ui/config/general.py:203
msgid "选择状态栏显示格式："
msgstr "Select Status Bar Display Format:"

#~ msgid "⚠️距离连胜重置还有："
#~ msgstr "⚠️Time until streak reset:"

#~ msgid "连胜中断"
#~ msgstr "Streak broken"

#~ msgid "今日累计使用："
#~ msgstr "Today's Total Usage:"


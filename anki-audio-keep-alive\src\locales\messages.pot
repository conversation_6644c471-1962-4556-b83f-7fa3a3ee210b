# Translations template for Audio Keep-Alive.
# Copyright (C) 2025 ORGANIZATION
# This file is distributed under the same license as the Audio Keep-Alive project.
# <AUTHOR> <EMAIL>, 2025.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Audio Keep-Alive VERSION\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-08-02 15:48+0900\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#: __init__.py:52
#, python-brace-format
msgid "无法解析Anki版本: {}"
msgstr ""

#: __init__.py:107
msgid "音频保活设置..."
msgstr ""

#: __init__.py:110
msgid "测试静音音频"
msgstr ""

#: __init__.py:118
msgid "警告: 无法添加音频保活菜单项"
msgstr ""

#: config/enums.py:15
msgid "连续播放"
msgstr ""

#: config/enums.py:16
msgid "间隔播放"
msgstr ""

#: config/enums.py:17
msgid "换卡时播放"
msgstr ""

#: config/enums.py:35
msgid "不显示"
msgstr ""

#: config/enums.py:36
msgid "仅显示图标"
msgstr ""

#: config/enums.py:37
msgid "显示音频状态"
msgstr ""

#: config/enums.py:38
msgid "显示图标+音频状态"
msgstr ""

#: config/enums.py:41
msgid "显示图标+音频状态+累计时间"
msgstr ""

#: config/constants.py:25
msgid "🎵播放中"
msgstr ""

#: config/constants.py:26
msgid "⏸️已暂停"
msgstr ""

#: audio_manager.py:40
msgid "音频保活已启动"
msgstr ""

#: audio_manager.py:52
msgid "音频保活已停止"
msgstr ""

#: audio_manager.py:102
msgid "测试音频播放"
msgstr ""

#: audio_manager.py:104
msgid "找不到音频文件"
msgstr ""

#: ui/config/dialog.py:28
msgid "音频保活设置"
msgstr ""

#: ui/config/dialog.py:33
msgid "常规设置"
msgstr ""

#: ui/config/dialog.py:65
msgid "配置已保存"
msgstr ""

#: ui/config/general.py:45
msgid "启用音频保活插件"
msgstr ""

#: ui/config/general.py:51
msgid "语言:"
msgstr ""

#: ui/config/general.py:58
msgid "音频设置"
msgstr ""

#: ui/config/general.py:62
msgid "启用音频播放"
msgstr ""

#: ui/config/general.py:67
msgid "播放模式:"
msgstr ""

#: ui/config/general.py:78
msgid "播放间隔(秒):"
msgstr ""

#: ui/config/general.py:85
msgid "音量:"
msgstr ""

#: ui/config/general.py:95
msgid "音频文件:"
msgstr ""

#: ui/config/general.py:97
msgid "选择文件"
msgstr ""

#: ui/config/general.py:99
msgid "未选择"
msgstr ""

#: ui/config/general.py:107
msgid "界面设置"
msgstr ""

#: ui/config/general.py:111
msgid "显示状态栏信息"
msgstr ""

#: ui/config/general.py:115
msgid "状态栏格式:"
msgstr ""

#: ui/config/general.py:131
msgid "选择音频文件"
msgstr ""

#: ui/config/general.py:134
msgid "音频文件 (*.opus *.mp3 *.wav *.ogg);;所有文件 (*)"
msgstr ""

#: ui/version_dialog.py:12
msgid "版本不兼容"
msgstr ""

#: ui/version_dialog.py:17
msgid "此插件需要 Anki {} 或更高版本。\n您当前的版本是 {}。\n\n请更新 Anki 以使用此插件。"
msgstr ""

#: ui/version_dialog.py:24
msgid "下载最新版本"
msgstr ""

#: ui/version_dialog.py:29
msgid "关闭"
msgstr ""

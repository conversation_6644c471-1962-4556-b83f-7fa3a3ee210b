# Translations template for PROJECT.
# Copyright (C) 2025 ORGANIZATION
# This file is distributed under the same license as the PROJECT project.
# <AUTHOR> <EMAIL>, 2025.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PROJECT VERSION\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-07-13 12:58+0900\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#: __init__.py:52
#, python-brace-format
msgid "无法解析Anki版本: {}"
msgstr ""

#: __init__.py:107
msgid "番茄钟 & 呼吸设置..."
msgstr ""

#: __init__.py:110
msgid "启动呼吸训练"
msgstr ""

#: __init__.py:119
msgid "警告: 无法添加番茄钟菜单项"
msgstr ""

#: hooks.py:60 pomodoro/pomodoro_manager.py:103
msgid "番茄钟计时器已停止。"
msgstr ""

#: hooks.py:78
#, python-brace-format
msgid "恭喜完成{target}个番茄钟！建议休息{minutes}分钟。"
msgstr ""

#: hooks.py:85
msgid "番茄钟时间到！"
msgstr ""

#: hooks.py:124
msgid "呼吸训练已跳过 (无启用阶段)。"
msgstr ""

#: hooks.py:129
msgid "呼吸训练已跳过 (循环次数为 0)。"
msgstr ""

#: hooks.py:135
msgid "呼吸训练完成！"
msgstr ""

#: hooks.py:137
msgid "呼吸训练已跳过。"
msgstr ""

#: hooks.py:139
msgid "跳过呼吸训练 (主窗口不可见)。"
msgstr ""

#: config/constants.py:48
msgid "⏳休息中："
msgstr ""

#: config/constants.py:49
msgid "⚠️休息上限："
msgstr ""

#: config/enums.py:21
msgid "左上角"
msgstr ""

#: config/enums.py:22
msgid "右上角"
msgstr ""

#: config/enums.py:23
msgid "左下角"
msgstr ""

#: config/enums.py:24
msgid "右下角"
msgstr ""

#: config/enums.py:25
msgid "上次使用的位置"
msgstr ""

#: config/enums.py:58
msgid "吸气"
msgstr ""

#: config/enums.py:64
msgid "屏气 (吸气后)"
msgstr ""

#: config/enums.py:70
msgid "呼气"
msgstr ""

#: config/enums.py:76
msgid "屏气 (呼气后)"
msgstr ""

#: config/enums.py:97
msgid "不显示"
msgstr ""

#: config/enums.py:98
msgid "仅显示图标"
msgstr ""

#: config/enums.py:99
msgid "仅显示倒计时"
msgstr ""

#: config/enums.py:100
msgid "仅显示进度"
msgstr ""

#: config/enums.py:103
msgid "显示图标+倒计时+进度"
msgstr ""

#: config/enums.py:107
msgid "显示图标+倒计时+进度+累计使用时间"
msgstr ""

#: pomodoro/pomodoro_manager.py:55
msgid "本次番茄钟结束"
msgstr ""

#: pomodoro/pomodoro_manager.py:83
msgid "番茄钟计时器已被禁用。"
msgstr ""

#: pomodoro/pomodoro_manager.py:96
#, python-brace-format
msgid "番茄钟计时器已启动，时长: {} 分钟。"
msgstr ""

#: pomodoro/pomodoro_manager.py:123
msgid "检测到长时间空闲，连胜中断。"
msgstr ""

#: pomodoro/pomodoro_manager.py:157
msgid "休息时间过长，番茄钟连胜已清空。"
msgstr ""

#: ui/version_dialog.py:13
msgid "请更新Anki"
msgstr ""

#: ui/version_dialog.py:21
#, python-brace-format
msgid "Anki番茄钟 & 呼吸训练插件需要Anki版本 <b>{required_version}</b> 或更高版本才能运行。"
msgstr ""

#: ui/version_dialog.py:24
#, python-brace-format
msgid "您当前使用的Anki版本是 <b>{current_version}</b>。"
msgstr ""

#: ui/version_dialog.py:28
msgid "请更新Anki到最新版本以使用此插件。"
msgstr ""

#: ui/version_dialog.py:34
msgid "跳转到Anki更新页面"
msgstr ""

#: ui/version_dialog.py:38
msgid "确定"
msgstr ""

#: ui/breathing/dialog.py:30 ui/config/dialog.py:36
msgid "呼吸训练"
msgstr ""

#: ui/breathing/dialog.py:46
msgid "准备..."
msgstr ""

#: ui/breathing/dialog.py:53 ui/breathing/dialog.py:84
#, python-brace-format
msgid "循环: {current} / {total}"
msgstr ""

#: ui/breathing/dialog.py:61
msgid "跳过训练"
msgstr ""

#: ui/circularTimer/core/window.py:48
msgid "番茄钟计时器"
msgstr ""

#: ui/config/breathing.py:46
msgid "呼吸训练设置"
msgstr ""

#: ui/config/breathing.py:50
msgid "目标循环次数:"
msgstr ""

#: ui/config/breathing.py:59
msgid "呼吸阶段设置"
msgstr ""

#: ui/config/breathing.py:85
msgid "选择音频"
msgstr ""

#: ui/config/breathing.py:87 ui/config/breathing.py:158
msgid "未选择"
msgstr ""

#: ui/config/breathing.py:97
msgid "持续时间:"
msgstr ""

#: ui/config/breathing.py:105
msgid "秒"
msgstr ""

#: ui/config/breathing.py:116
msgid "预计时间: --:--"
msgstr ""

#: ui/config/breathing.py:148
#, python-brace-format
msgid "为 {phase_key} 阶段选择音频文件"
msgstr ""

#: ui/config/breathing.py:150
msgid "音频文件 (*.wav *.mp3 *.opus *.ogg);;所有文件 (*)"
msgstr ""

#: ui/config/dialog.py:28
msgid "番茄钟/呼吸训练设置"
msgstr ""

#: ui/config/dialog.py:35 ui/config/general.py:42
msgid "常规设置"
msgstr ""

#: ui/config/dialog.py:98
msgid "预计时间: --:-- (未启用或周期为0)"
msgstr ""

#: ui/config/dialog.py:105
#, python-brace-format
msgid "预计时间: {mins:02d}:{secs:02d}"
msgstr ""

#: ui/config/dialog.py:111
msgid "预计时间: 计算错误"
msgstr ""

#: ui/config/dialog.py:134
msgid "配置已保存"
msgstr ""

#: ui/config/dialog.py:142
#, python-brace-format
msgid "保存配置时出错: {}"
msgstr ""

#: ui/config/general.py:51
msgid "启用番茄钟插件"
msgstr ""

#: ui/config/general.py:57
msgid "语言:"
msgstr ""

#: ui/config/general.py:66
msgid "全局计时器"
msgstr ""

#: ui/config/general.py:72
msgid "显示圆形计时器"
msgstr ""

#: ui/config/general.py:78
msgid "圆形计时器样式:"
msgstr ""

#: ui/config/general.py:91
msgid "计时器窗口位置:"
msgstr ""

#: ui/config/general.py:104
msgid "连胜上限:"
msgstr ""

#: ui/config/general.py:109 ui/config/general.py:130
msgid "个番茄钟"
msgstr ""

#: ui/config/general.py:117
msgid "连续完成指定数量的番茄钟后，将进行长休息"
msgstr ""

#: ui/config/general.py:123
msgid "进度显示阈值:"
msgstr ""

#: ui/config/general.py:143
msgid "当目标番茄钟数量超过此值时，将以 '🍅 x N' 的形式显示进度"
msgstr ""

#: ui/config/general.py:152
msgid "番茄钟时长:"
msgstr ""

#: ui/config/general.py:157 ui/config/general.py:171 ui/config/general.py:185
msgid "分钟"
msgstr ""

#: ui/config/general.py:166
msgid "长休息时长:"
msgstr ""

#: ui/config/general.py:180
msgid "休息时间上限:"
msgstr ""

#: ui/config/general.py:193
msgid "超过休息时间上限后，累计的番茄钟将归零"
msgstr ""

#: ui/config/general.py:201
msgid "状态栏显示设置"
msgstr ""

#: ui/config/general.py:203
msgid "选择状态栏显示格式："
msgstr ""


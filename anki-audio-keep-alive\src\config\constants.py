from enum import Enum

from ..translator import _
from .enums import AudioPlaybackMode, StatusBarFormat


class AnkiStates(str, Enum):
    STARTUP = "startup"
    DECK_BROWSER = "deckBrowser"
    OVERVIEW = "overview"
    REVIEW = "review"
    RESET_REQUIRED = "resetRequired"
    PROFILE_MANAGER = "profileManager"


class Defaults:
    # Audio settings
    AUDIO_ENABLED = True
    PLAYBACK_MODE = AudioPlaybackMode.INTERVAL
    INTERVAL_SECONDS = 30
    VOLUME = 0.1  # Very low volume for silent audio
    AUDIO_FORMAT = "opus"
    
    # UI settings
    SHOW_STATUS_BAR = True
    STATUS_BAR_FORMAT = StatusBarFormat.ICON_AUDIO_STATUS_WITH_TIME
    
    # Status bar display constants
    class StatusBar:
        AUDIO_ACTIVE = "🔊"  # Audio active icon
        AUDIO_INACTIVE = "🔇"  # Audio inactive icon
        AUDIO_PLAYING = _("🎵播放中")  # Audio playing status
        AUDIO_PAUSED = _("⏸️已暂停")  # Audio paused status
        TEXT = f"{AUDIO_ACTIVE} --:--"
        FORMAT = StatusBarFormat.ICON_AUDIO_STATUS_WITH_TIME


# Default silent audio file name
SILENT_AUDIO_FILE = "silent.opus"

name: Build Anki Addon

on:
  push:
    branches: [main]
  workflow_dispatch: {}

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Set Filename
        id: set_vars
        run: |
          REPO_NAME=${{ github.event.repository.name }}
          TIMESTAMP=$(date +'%Y%m%d-%H%M%S')
          echo "FILENAME=${REPO_NAME}-${TIMESTAMP}.ankiaddon" >> $GITHUB_ENV
          echo "Generated filename: ${REPO_NAME}-${TIMESTAMP}.ankiaddon"

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.9"

      - name: Install dependencies to vendor directory
        run: |
          pip install --upgrade pip setuptools wheel
          pip install --target=src/vendor -r requirements.txt

      - name: Upload Artifact
        uses: actions/upload-artifact@v4
        with:
          name: ${{ env.FILENAME }}
          path: src

from aqt import QDialog, QMainWindow, QObject, mw
from PyQt6.QtCore import QUrl
from PyQt6.QtMultimedia import QAudioOutput, QMediaPlayer


class AudioPlayer(QObject):
    """Audio player for silent audio files to keep audio devices active."""
    
    def __init__(self, parent: QMainWindow | QDialog = mw):
        super().__init__(parent)
        self.player = QMediaPlayer(parent)
        self._audio_output = QAudioOutput()
        self.player.setAudioOutput(self._audio_output)
        self._cache: dict[str, QUrl] = {}  # Cache for audio files: {file_path: QUrl}
        self._is_playing = False

    def play(self, file_path: str, volume: float = 0.1):
        """Play the audio file, using cached version if available."""
        if not file_path:
            return

        # Use cached URL if available
        if file_path in self._cache:
            url: QUrl = self._cache[file_path]
        else:
            url: QUrl = QUrl.fromLocalFile(file_path)
            self._cache[file_path] = url

        # Set very low volume for silent audio
        self._audio_output.setVolume(volume)
        
        self.player.setSource(url)
        self.player.play()
        self._is_playing = True

    def stop(self):
        """Stop the audio playback."""
        self.player.stop()
        self._is_playing = False

    def pause(self):
        """Pause the audio playback."""
        self.player.pause()
        self._is_playing = False

    def resume(self):
        """Resume the audio playback."""
        self.player.play()
        self._is_playing = True

    def is_playing(self) -> bool:
        """Check if audio is currently playing."""
        return self._is_playing

    def set_volume(self, volume: float):
        """Set the audio volume (0.0 to 1.0)."""
        self._audio_output.setVolume(max(0.0, min(1.0, volume)))
